//
//  ScratchCardGridView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡网格视图
 * 实现刮刮卡网格布局，包括卡片项目视图、动画效果、点击处理等
 * 基于ztt1项目的ScratchCardGridView，适配ztt2项目
 */
struct ScratchCardGridView: View {
    
    // MARK: - Properties
    
    @Binding var cardItems: [ScratchCardItem]
    let onCardTapped: (Int) -> Void
    
    // MARK: - Constants
    
    private let columns = Array(repeating: GridItem(.flexible(), spacing: 16), count: 3)
    private let itemSize = CGSize(width: 100, height: 120)
    
    // MARK: - Body
    
    var body: some View {
        LazyVGrid(columns: columns, spacing: 20) {
            ForEach(cardItems.indices, id: \.self) { index in
                scratchCardItemView(cardItem: cardItems[index], index: index)
            }
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Scratch Card Item View
    
    /**
     * 单张刮刮卡项目视图
     */
    private func scratchCardItemView(cardItem: ScratchCardItem, index: Int) -> some View {
        VStack(spacing: 8) {
            // 刮刮卡主体
            ScratchCardItemView(
                cardItem: cardItem,
                size: itemSize,
                onTap: {
                    onCardTapped(index)
                }
            )
            .scratchCardFloating(
                offset: CGFloat.random(in: 4...8),
                rotation: Double.random(in: 1...4),
                delay: Double(index) * 0.2,
                duration: Double.random(in: 2.0...2.8),
                enabled: !cardItem.isScratched && cardItem.animationState == .idle
            )
            
            // 刮刮卡信息
            cardInfoView(cardItem: cardItem, index: index)
        }
        .contentShape(Rectangle()) // 扩大点击区域
    }
    
    /**
     * 刮刮卡信息视图
     */
    private func cardInfoView(cardItem: ScratchCardItem, index: Int) -> some View {
        VStack(spacing: 4) {
            // 卡片标题
            Text(cardItem.displayTitle)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.primary)
            
            // 状态标签
            statusLabel(for: cardItem)
        }
    }
    
    /**
     * 状态标签
     */
    private func statusLabel(for cardItem: ScratchCardItem) -> some View {
        Text(cardItem.statusDescription)
            .font(.system(size: 10, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 2)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(statusColor(for: cardItem))
            )
    }
    
    /**
     * 获取状态颜色
     */
    private func statusColor(for cardItem: ScratchCardItem) -> Color {
        if cardItem.isScratched {
            return .green
        } else if cardItem.animationState == .scratching {
            return .orange
        } else {
            return .blue
        }
    }
}

/**
 * 刮刮卡项目视图
 */
struct ScratchCardItemView: View {
    
    // MARK: - Properties
    
    let cardItem: ScratchCardItem
    let size: CGSize
    let onTap: () -> Void
    
    // MARK: - State Properties
    
    @State private var isPressed = false
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // 卡片背景
            cardBackground
            
            // 卡片内容
            cardContent
            
            // 刮除状态覆盖层
            if cardItem.isScratched {
                scratchedOverlay
            }
        }
        .frame(width: size.width, height: size.height)
        .scaleEffect(cardItem.scaleEffect * (isPressed ? 0.95 : 1.0))
        .opacity(cardItem.opacity)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        .animation(.easeInOut(duration: 0.6), value: cardItem.animationState)
        .onTapGesture {
            if cardItem.isClickable {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
                
                onTap()
            }
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing && cardItem.isClickable
        }, perform: {})
    }
    
    // MARK: - Card Background
    
    /**
     * 卡片背景
     */
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(
                LinearGradient(
                    colors: [Color.white, Color.gray.opacity(0.1)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Card Content
    
    /**
     * 卡片内容
     */
    private var cardContent: some View {
        VStack(spacing: 8) {
            // 刮刮卡图片
            Image(cardItem.isScratched ? "刮刮卡中奖" : "刮刮卡")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 50, height: 50)
            
            // 奖品名称（只在已刮开时显示）
            if cardItem.isScratched {
                Text(cardItem.prizeName)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            } else {
                Text("刮一刮")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.gray)
            }
        }
        .padding(8)
    }
    
    // MARK: - Scratched Overlay
    
    /**
     * 已刮开覆盖层
     */
    private var scratchedOverlay: some View {
        RoundedRectangle(cornerRadius: 12)
            .stroke(Color.green, lineWidth: 2)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.green.opacity(0.1))
            )
    }
}

// MARK: - Floating Animation Extension

extension View {
    func scratchCardFloating(offset: CGFloat, rotation: Double, delay: Double, duration: Double, enabled: Bool) -> some View {
        self.floating(
            offset: offset,
            rotation: rotation,
            delay: delay,
            duration: duration,
            enabled: enabled
        )
    }
}

// MARK: - Preview

#Preview {
    let sampleCards = [
        ScratchCardItem.create(index: 0, prizeName: "小贴纸"),
        ScratchCardItem.create(index: 1, prizeName: "铅笔"),
        ScratchCardItem.create(index: 2, prizeName: "橡皮"),
        ScratchCardItem.create(index: 3, prizeName: "尺子"),
        ScratchCardItem.create(index: 4, prizeName: "小玩具"),
        ScratchCardItem.create(index: 5, prizeName: "糖果")
    ]
    
    return ScrollView {
        ScratchCardGridView(
            cardItems: .constant(sampleCards),
            onCardTapped: { index in
                print("点击了刮刮卡 \(index)")
            }
        )
        .padding()
    }
}
