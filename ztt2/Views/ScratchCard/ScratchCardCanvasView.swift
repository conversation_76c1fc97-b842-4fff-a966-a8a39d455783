//
//  ScratchCardCanvasView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡画布视图
 * 实现刮除手势、粒子效果、进度计算等核心功能
 * 基于ztt1项目的ScratchCardCanvasView，适配ztt2项目
 */
struct ScratchCardCanvasView: View {
    
    // MARK: - Properties
    
    @State var cardItem: ScratchCardItem
    let onProgressUpdate: (Double) -> Void
    let onScratchComplete: () -> Void
    
    // MARK: - State Properties
    
    @State private var scratchedPath = Path()
    @State private var scratchParticles: [ScratchParticle] = []
    @State private var isScratching = false
    @State private var currentProgress: Double = 0.0
    
    // MARK: - Constants
    
    private let cardSize = CGSize(width: 280, height: 200)
    private let brushRadius: CGFloat = 15
    private let completeThreshold: Double = 0.6
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // 底层奖品内容
            prizeContentLayer
                .clipShape(RoundedRectangle(cornerRadius: 20))
            
            // 刮除遮挡层
            scratchableLayer
                .clipShape(RoundedRectangle(cornerRadius: 20))
            
            // 刮除粒子效果
            scratchParticlesLayer
        }
        .frame(width: cardSize.width, height: cardSize.height)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 4)
        )
        .gesture(
            DragGesture(minimumDistance: 0)
                .onChanged(handleDragChanged)
                .onEnded(handleDragEnded)
        )
    }
    
    // MARK: - Prize Content Layer
    
    /**
     * 奖品内容层
     */
    private var prizeContentLayer: some View {
        ZStack {
            // 背景渐变
            LinearGradient(
                colors: [Color.orange.opacity(0.8), Color.red.opacity(0.6)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            VStack(spacing: 12) {
                // 中奖图片
                Image("刮刮卡中奖")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 80, height: 80)
                
                // 奖品名称
                Text(cardItem.prizeName)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                
                // 恭喜文字
                Text("恭喜中奖！")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))
            }
        }
    }
    
    // MARK: - Scratchable Layer
    
    /**
     * 可刮除遮挡层
     */
    private var scratchableLayer: some View {
        Canvas { context, size in
            // 绘制完全不透明的遮挡层背景
            let backgroundRect = CGRect(origin: .zero, size: size)
            context.fill(
                Path(backgroundRect),
                with: .linearGradient(
                    Gradient(colors: [
                        Color.gray,
                        Color.gray.opacity(1)
                    ]),
                    startPoint: CGPoint(x: 0, y: 0),
                    endPoint: CGPoint(x: size.width, y: size.height)
                )
            )
            
            // 添加纹理效果
            addScratchableTexture(context: context, size: size)
            
            // 绘制刮除区域（使用blend mode实现挖空效果）
            context.blendMode = .destinationOut
            context.fill(scratchedPath, with: .color(.black))
        }
    }
    
    /**
     * 添加可刮除层纹理
     */
    private func addScratchableTexture(context: GraphicsContext, size: CGSize) {
        // 添加银色刮刮卡质感（不透明）
        let dotSpacing: CGFloat = 8
        let dotSize: CGFloat = 2
        
        for x in stride(from: 0, to: size.width, by: dotSpacing) {
            for y in stride(from: 0, to: size.height, by: dotSpacing) {
                let dot = Path(ellipseIn: CGRect(
                    x: x,
                    y: y,
                    width: dotSize,
                    height: dotSize
                ))
                context.fill(dot, with: .color(.white.opacity(0.5)))
            }
        }
        
        // 添加刮刮卡图片
        let scratchCardImage = Image("刮刮卡")
        context.draw(
            scratchCardImage,
            in: CGRect(x: size.width/2 - 40, y: size.height/2 - 40, width: 80, height: 80)
        )
        
        // 添加"刮一刮"提示文字（不透明）
        let scratchHint = "刮一刮"
        context.draw(
            Text(scratchHint)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(.white),
            at: CGPoint(x: size.width / 2, y: size.height / 2 + 60)
        )
    }
    
    // MARK: - Scratch Particles Layer
    
    /**
     * 刮除粒子效果层
     */
    private var scratchParticlesLayer: some View {
        ZStack {
            ForEach(scratchParticles, id: \.id) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.position)
                    .opacity(particle.opacity)
                    .scaleEffect(particle.scale)
            }
        }
        .allowsHitTesting(false)
    }
    
    // MARK: - Gesture Handlers
    
    /**
     * 处理拖拽手势变化
     */
    private func handleDragChanged(value: DragGesture.Value) {
        let location = value.location
        
        // 确保刮除位置在卡片范围内
        guard location.x >= 0 && location.x <= cardSize.width &&
              location.y >= 0 && location.y <= cardSize.height else {
            return
        }
        
        if !isScratching {
            isScratching = true
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }
        
        // 添加刮除圆形区域
        let scratchCircle = Path(ellipseIn: CGRect(
            x: location.x - brushRadius,
            y: location.y - brushRadius,
            width: brushRadius * 2,
            height: brushRadius * 2
        ))
        
        scratchedPath.addPath(scratchCircle)
        
        // 生成刮除粒子
        generateScratchParticles(at: location)
        
        // 计算刮除进度
        let newProgress = calculateScratchProgress()
        if newProgress != currentProgress {
            currentProgress = newProgress
            onProgressUpdate(newProgress)
            
            // 检查是否达到完成阈值
            if newProgress >= completeThreshold && !cardItem.isPrizeRevealed {
                // 延迟触发完成，让用户看到最后的刮除效果
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    onScratchComplete()
                }
            }
        }
    }
    
    /**
     * 处理拖拽手势结束
     */
    private func handleDragEnded(value: DragGesture.Value) {
        isScratching = false
        
        // 逐渐清除刮除粒子
        withAnimation(.easeOut(duration: 1.0)) {
            scratchParticles.removeAll()
        }
    }
    
    // MARK: - Helper Methods
    
    /**
     * 计算刮除进度
     */
    private func calculateScratchProgress() -> Double {
        // 简化的进度计算：基于刮除路径的复杂度
        let pathBounds = scratchedPath.boundingRect
        let totalArea = cardSize.width * cardSize.height
        let scratchedArea = pathBounds.width * pathBounds.height
        
        let progress = min(1.0, scratchedArea / totalArea * 3.0) // 乘以3.0使进度更敏感
        return progress
    }
    
    // MARK: - Particle Generation
    
    /**
     * 生成刮除粒子
     */
    private func generateScratchParticles(at location: CGPoint) {
        // 限制粒子数量，避免性能问题
        if scratchParticles.count > 20 {
            scratchParticles.removeFirst(5)
        }
        
        // 生成新粒子
        for _ in 0..<3 {
            let particle = ScratchParticle(
                position: CGPoint(
                    x: location.x + CGFloat.random(in: -10...10),
                    y: location.y + CGFloat.random(in: -10...10)
                ),
                velocity: CGPoint(
                    x: CGFloat.random(in: -30...30),
                    y: CGFloat.random(in: -50...10)
                ),
                color: cardItem.cardSkin.coverColor.opacity(0.6),
                size: CGFloat.random(in: 2...4),
                lifespan: 0.8
            )
            scratchParticles.append(particle)
        }
        
        // 更新现有粒子
        updateScratchParticles()
    }
    
    /**
     * 更新刮除粒子
     */
    private func updateScratchParticles() {
        let deltaTime = 0.016 // 约60fps
        
        for i in scratchParticles.indices.reversed() {
            scratchParticles[i].update(deltaTime: deltaTime)
            
            // 移除已死亡的粒子
            if !scratchParticles[i].isAlive {
                scratchParticles.remove(at: i)
            }
        }
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        ScratchCardCanvasView(
            cardItem: ScratchCardItem.create(index: 0, prizeName: "小贴纸"),
            onProgressUpdate: { progress in
                print("刮除进度: \(progress)")
            },
            onScratchComplete: {
                print("刮除完成!")
            }
        )
    }
}
