//
//  ScratchCardView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡主视图
 * 创建刮刮卡主页面，集成网格视图、结果弹窗、积分检查等功能
 * 基于ztt1项目的ScratchCardView，适配ztt2项目架构
 */
struct ScratchCardView: View {
    
    // MARK: - Properties
    
    let member: Member
    let onDismiss: () -> Void
    let onNavigateToSettings: (() -> Void)?
    
    // MARK: - State Properties
    
    @StateObject private var viewModel: ScratchCardViewModel
    @State private var pageAppeared = false
    
    // MARK: - Environment
    
    @EnvironmentObject private var dataManager: DataManager
    
    // MARK: - Initialization
    
    init(member: Member, onDismiss: @escaping () -> Void, onNavigateToSettings: (() -> Void)? = nil) {
        self.member = member
        self.onDismiss = onDismiss
        self.onNavigateToSettings = onNavigateToSettings
        self._viewModel = StateObject(wrappedValue: ScratchCardViewModel(member: member))
    }
    
    // MARK: - Body
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                backgroundGradient
                
                // 主要内容
                mainContent(geometry: geometry)
                
                // 刮除覆盖层
                if viewModel.showScratchOverlay, let selectedIndex = viewModel.selectedCardIndex {
                    scratchOverlay(cardIndex: selectedIndex)
                        .allowsHitTesting(true)
                }
                
                // 结果弹窗
                if viewModel.showResult {
                    resultOverlay
                }
                
                // 积分不足提示
                if viewModel.showInsufficientPoints {
                    insufficientPointsAlert
                }
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("刮刮卡")
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                backButton
            }
        }
        .onAppear {
            viewModel.loadScratchCardConfig()
            withAnimation(.easeOut(duration: 0.8)) {
                pageAppeared = true
            }
        }
    }
    
    // MARK: - Background Gradient
    
    /**
     * 背景渐变
     */
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color.orange.opacity(0.3),
                Color.red.opacity(0.2),
                Color.purple.opacity(0.1)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Main Content
    
    /**
     * 主要内容
     */
    private func mainContent(geometry: GeometryProxy) -> some View {
        ScrollView {
            VStack(spacing: 20) {
                // 标题区域
                titleSection
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : -30)
                    .animation(.easeOut(duration: 0.8).delay(0.2), value: pageAppeared)
                
                // 积分信息
                pointsInfoSection
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : -20)
                    .animation(.easeOut(duration: 0.8).delay(0.4), value: pageAppeared)
                
                // 内容区域
                contentArea
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : 30)
                    .animation(.easeOut(duration: 0.8).delay(0.6), value: pageAppeared)
            }
            .padding(.top, 20)
            .padding(.horizontal, 20)
        }
    }
    
    // MARK: - Title Section
    
    /**
     * 标题区域
     */
    private var titleSection: some View {
        VStack(spacing: 12) {
            Text("开心刮刮卡")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.primary)
            
            Text("刮开惊喜，收获快乐！")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Points Info Section
    
    /**
     * 积分信息区域
     */
    private var pointsInfoSection: some View {
        HStack(spacing: 20) {
            // 当前积分
            VStack(spacing: 4) {
                Text("当前积分")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
                
                Text("\(viewModel.memberCurrentPoints)")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.blue)
            }
            
            Divider()
                .frame(height: 40)
            
            // 每次消耗
            VStack(spacing: 4) {
                Text("每次消耗")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
                
                Text("\(viewModel.costPerPlay)")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.orange)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.8))
                .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Content Area
    
    /**
     * 内容区域
     */
    private var contentArea: some View {
        VStack(spacing: 20) {
            if viewModel.isLoading {
                loadingView
            } else if viewModel.showNoConfig {
                emptyStateView
            } else {
                scratchCardGridView
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Loading View
    
    /**
     * 加载视图
     */
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("加载刮刮卡中...")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
    }
    
    // MARK: - Empty State View
    
    /**
     * 空状态视图
     */
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image("刮刮卡")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 100, height: 100)
                .opacity(0.6)
            
            Text("暂无刮刮卡配置")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.secondary)
            
            Text("请先配置刮刮卡奖品和规则")
                .font(.system(size: 14))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button(action: {
                onDismiss()
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    onNavigateToSettings?()
                }
            }) {
                Text("去配置")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.orange)
                    )
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.horizontal, 40)
        }
        .frame(maxWidth: .infinity, minHeight: 300)
    }
    
    // MARK: - Scratch Card Grid View
    
    /**
     * 刮刮卡网格视图
     */
    private var scratchCardGridView: some View {
        ScratchCardGridView(
            cardItems: $viewModel.cardItems,
            onCardTapped: { index in
                viewModel.selectCard(at: index)
            }
        )
    }
    
    // MARK: - Back Button

    /**
     * 返回按钮
     */
    private var backButton: some View {
        Button(action: onDismiss) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                Text("返回")
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(.primary)
        }
    }

    // MARK: - Scratch Overlay

    /**
     * 刮除覆盖层
     */
    private func scratchOverlay(cardIndex: Int) -> some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.8)
                .ignoresSafeArea()
                .onTapGesture {
                    // 防止点击背景关闭
                }

            VStack(spacing: 20) {
                // 添加"开心刮刮卡"标题
                VStack(spacing: 20) {
                    Text("开心刮刮卡")
                        .font(.system(size: 30, weight: .bold))
                        .foregroundColor(.white)
                        .shadow(color: Color.black.opacity(0.3), radius: 2, x: 0, y: 1)

                    Text("用手指刮开涂层，看看你的幸运奖品！")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding(.top, 80)

                // 刮刮卡
                ScratchCardCanvasView(
                    cardItem: viewModel.cardItems[cardIndex],
                    onProgressUpdate: { progress in
                        viewModel.updateScratchProgress(index: cardIndex, progress: progress)
                    },
                    onScratchComplete: {
                        viewModel.revealPrize(at: cardIndex)
                    }
                )
                .scaleEffect(1.2)

                Spacer()
            }
        }
        .transition(.opacity)
    }

    // MARK: - Result Overlay

    /**
     * 结果弹窗
     */
    private var resultOverlay: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.6)
                .ignoresSafeArea()
                .onTapGesture {
                    // 防止点击背景关闭
                }

            // 结果内容
            VStack(spacing: 24) {
                // 庆祝图标
                Image("刮刮卡中奖")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 80, height: 80)

                // 恭喜文字
                Text("恭喜中奖！")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.primary)

                // 奖品名称
                Text(viewModel.resultPrize)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.orange)
                    .multilineTextAlignment(.center)

                // 消耗积分
                Text("消耗积分：\(viewModel.costPerPlay)")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)

                // 确认按钮
                Button(action: {
                    viewModel.confirmResult()
                }) {
                    Text("确认")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 14)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.orange)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 40)
        }
        .transition(.scale.combined(with: .opacity))
    }

    // MARK: - Insufficient Points Alert

    /**
     * 积分不足提示
     */
    private var insufficientPointsAlert: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.6)
                .ignoresSafeArea()
                .onTapGesture {
                    viewModel.showInsufficientPoints = false
                }

            // 提示内容
            VStack(spacing: 20) {
                // 警告图标
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.orange)

                // 标题
                Text("积分不足")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)

                // 提示信息
                Text(viewModel.insufficientPointsMessage)
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)

                // 确认按钮
                Button(action: {
                    viewModel.showInsufficientPoints = false
                }) {
                    Text("我知道了")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.orange)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 40)
        }
        .transition(.scale.combined(with: .opacity))
    }
}

// MARK: - Preview

#Preview {
    let persistenceController = PersistenceController.preview
    let context = persistenceController.container.viewContext

    // 创建测试成员
    let testMember = Member(context: context)
    testMember.id = UUID()
    testMember.name = "测试成员"
    testMember.role = "儿子"
    testMember.currentPoints = 100

    return NavigationView {
        ScratchCardView(
            member: testMember,
            onDismiss: {
                print("关闭刮刮卡")
            },
            onNavigateToSettings: {
                print("导航到设置")
            }
        )
    }
    .environmentObject(DataManager.shared)
}
