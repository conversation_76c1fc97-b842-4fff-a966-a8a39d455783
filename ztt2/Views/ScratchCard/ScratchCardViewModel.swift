//
//  ScratchCardViewModel.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI
import Combine

/**
 * 刮刮卡视图模型
 * 实现刮刮卡的业务逻辑，包括配置加载、状态管理、奖品分配等
 * 基于ztt1项目的ScratchCardViewModel，适配ztt2项目架构
 */
class ScratchCardViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var cardItems: [ScratchCardItem] = []
    @Published var isLoading = false
    @Published var showScratchOverlay = false
    @Published var selectedCardIndex: Int?
    @Published var showResult = false
    @Published var resultPrize = ""
    @Published var showInsufficientPoints = false
    @Published var showNoConfig = false
    @Published var costPerPlay = 0
    @Published var prizeRevealed = false
    
    // MARK: - Private Properties
    
    private let member: Member
    private let dataManager = DataManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(member: Member) {
        self.member = member
    }
    
    // MARK: - Public Methods
    
    /**
     * 加载刮刮卡配置
     */
    func loadScratchCardConfig() {
        isLoading = true
        showNoConfig = false
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.performConfigLoad()
        }
    }
    
    /**
     * 点击刮刮卡
     */
    func selectCard(at index: Int) {
        guard index < cardItems.count else { return }
        
        let card = cardItems[index]
        guard card.isClickable else { return }
        
        // 检查积分是否足够
        if member.currentPoints < costPerPlay {
            showInsufficientPoints = true
            return
        }
        
        // 扣除积分
        deductPoints()
        
        // 设置选中的卡片
        selectedCardIndex = index
        cardItems[index].startScratching()
        
        // 显示刮除覆盖层
        withAnimation(.easeInOut(duration: 0.3)) {
            showScratchOverlay = true
        }
    }
    
    /**
     * 更新刮除进度
     */
    func updateScratchProgress(index: Int, progress: Double) {
        guard index < cardItems.count else { return }
        cardItems[index].updateScratchProgress(progress)
    }
    
    /**
     * 显示奖品
     */
    func revealPrize(at index: Int) {
        guard index < cardItems.count else { return }
        
        prizeRevealed = true
        cardItems[index].animationState = .revealing
        
        // 使用被刮开卡片的奖品，确保显示与获得的奖品一致
        let prize = cardItems[index].prizeName
        resultPrize = prize
        
        // 生成庆祝粒子
        generateCelebrationParticles()
        
        // 延迟显示结果弹窗
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                self.showResult = true
            }
        }
    }
    
    /**
     * 确认结果
     */
    func confirmResult() {
        // 完成刮除
        if let selectedIndex = selectedCardIndex {
            cardItems[selectedIndex].completeScratching()
        }
        
        // 创建抽奖记录
        createLotteryRecord()
        
        // 重置状态
        resetScratchState()
        
        // 触觉反馈
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }
    
    /**
     * 重置刮除状态
     */
    func resetScratchState() {
        showScratchOverlay = false
        showResult = false
        selectedCardIndex = nil
        prizeRevealed = false
        resultPrize = ""
    }
    
    // MARK: - Private Methods
    
    /**
     * 执行配置加载
     */
    private func performConfigLoad() {
        guard let config = dataManager.getScratchCardConfig(for: member) else {
            isLoading = false
            showNoConfig = true
            return
        }
        
        costPerPlay = Int(config.costPerPlay)
        
        // 创建刮刮卡项目
        let items = config.allItems.sorted { $0.itemIndex < $1.itemIndex }
        cardItems = items.enumerated().map { index, item in
            ScratchCardItem.create(
                index: index,
                prizeName: item.prizeName ?? "未知奖品"
            )
        }
        
        isLoading = false
        
        print("✅ 刮刮卡配置加载成功: 成员=\(member.displayName), 卡片数=\(cardItems.count), 消耗积分=\(costPerPlay)")
    }
    
    /**
     * 扣除积分
     */
    private func deductPoints() {
        // 使用addPointRecord方法扣除积分（传入负值）
        dataManager.addPointRecord(
            to: member,
            reason: "刮刮卡抽奖",
            value: Int32(-costPerPlay),
            recordType: "lottery"
        )

        print("✅ 积分扣除成功: 成员=\(member.displayName), 扣除=\(costPerPlay)")
    }
    
    /**
     * 创建抽奖记录
     */
    private func createLotteryRecord() {
        dataManager.createLotteryRecord(
            for: member,
            toolType: "scratchcard",
            prizeResult: resultPrize,
            cost: Int32(costPerPlay)
        )

        print("✅ 刮刮卡抽奖记录创建成功: 成员=\(member.displayName), 奖品=\(resultPrize)")
    }
    
    /**
     * 生成庆祝粒子
     */
    private func generateCelebrationParticles() {
        // 这里可以添加庆祝粒子效果的实现
        // 暂时使用触觉反馈代替
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            impactFeedback.impactOccurred()
        }
    }
}

// MARK: - Helper Extensions

extension ScratchCardViewModel {
    
    /**
     * 获取成员当前积分
     */
    var memberCurrentPoints: Int {
        return Int(member.currentPoints)
    }
    
    /**
     * 检查是否有足够积分
     */
    var hasEnoughPoints: Bool {
        return memberCurrentPoints >= costPerPlay
    }
    
    /**
     * 获取积分不足提示信息
     */
    var insufficientPointsMessage: String {
        let needed = costPerPlay - memberCurrentPoints
        return "积分不足！还需要 \(needed) 积分才能进行刮刮卡抽奖。"
    }
}
