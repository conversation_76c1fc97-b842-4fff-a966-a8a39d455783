//
//  ScratchCardTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡功能测试视图
 * 用于测试刮刮卡的完整功能流程
 */
struct ScratchCardTestView: View {
    
    // MARK: - State Properties
    
    @State private var showScratchCard = false
    @State private var selectedOption = ""
    @State private var showResult = false
    @State private var testMember: Member?
    
    // MARK: - Environment
    
    @EnvironmentObject private var dataManager: DataManager
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题
                VStack(spacing: 12) {
                    Text("刮刮卡功能测试")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.primary)
                    
                    Text("测试刮刮卡的完整功能流程")
                        .font(.system(size: 16))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 40)
                
                Spacer()
                
                // 测试按钮
                VStack(spacing: 20) {
                    // 刮刮卡测试按钮
                    Button(action: {
                        setupTestMemberAndShowScratchCard()
                    }) {
                        HStack(spacing: 12) {
                            Image("刮刮卡")
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 30, height: 30)
                            
                            Text("测试刮刮卡功能")
                                .font(.system(size: 18, weight: .medium))
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.orange)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    // 配置刮刮卡按钮
                    Button(action: {
                        setupScratchCardConfig()
                    }) {
                        HStack(spacing: 12) {
                            Image(systemName: "gearshape.fill")
                                .font(.system(size: 20))
                            
                            Text("配置测试刮刮卡")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 14)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.blue)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.horizontal, 40)
                
                Spacer()
                
                // 结果显示
                if showResult {
                    resultView
                        .transition(.scale.combined(with: .opacity))
                }
                
                Spacer()
                
                // 说明文字
                VStack(spacing: 8) {
                    Text("测试说明:")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.secondary)
                    
                    Text("1. 先点击\"配置测试刮刮卡\"创建配置\n2. 再点击\"测试刮刮卡功能\"进入刮刮卡页面\n3. 点击刮刮卡进行刮除测试")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                }
                .padding(.horizontal, 40)
                .padding(.bottom, 50)
            }
            .navigationTitle("刮刮卡测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .fullScreenCover(isPresented: $showScratchCard) {
            if let member = testMember {
                NavigationView {
                    ScratchCardView(
                        member: member,
                        onDismiss: {
                            showScratchCard = false
                            selectedOption = "刮刮卡测试完成"
                            showResultWithAnimation()
                        },
                        onNavigateToSettings: {
                            selectedOption = "导航到配置页面"
                            showResultWithAnimation()
                        }
                    )
                }
                .environmentObject(dataManager)
            }
        }
        .onAppear {
            createTestMemberIfNeeded()
        }
    }
    
    // MARK: - Result View
    
    private var resultView: some View {
        VStack(spacing: 12) {
            Text("测试结果")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.primary)
            
            Text(selectedOption)
                .font(.system(size: 14))
                .foregroundColor(.blue)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 20)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.blue.opacity(0.1))
                )
        }
    }
    
    // MARK: - Helper Methods
    
    /**
     * 创建测试成员
     */
    private func createTestMemberIfNeeded() {
        // 检查是否已有测试成员
        let existingMembers = dataManager.members.filter { $0.name == "刮刮卡测试成员" }
        if let existing = existingMembers.first {
            testMember = existing
            return
        }

        // 创建新的测试成员
        let member = dataManager.createMember(
            name: "刮刮卡测试成员",
            role: "测试",
            birthDate: nil,
            initialPoints: 1000
        )

        if let member = member {
            testMember = member
            print("✅ 创建测试成员成功")
        } else {
            print("❌ 创建测试成员失败")
        }
    }
    
    /**
     * 配置刮刮卡
     */
    private func setupScratchCardConfig() {
        guard let member = testMember else {
            print("❌ 测试成员不存在")
            return
        }
        
        let cardPrizes = ["小贴纸", "铅笔", "橡皮", "尺子", "小玩具", "糖果"]
        
        let success = dataManager.saveScratchCardConfig(
            for: member,
            cardCount: 6,
            costPerPlay: 20,
            cardPrizes: cardPrizes
        )
        
        if success != nil {
            selectedOption = "刮刮卡配置创建成功！\n6张刮刮卡，每次消耗20积分"
            showResultWithAnimation()
            print("✅ 刮刮卡配置创建成功")
        } else {
            selectedOption = "刮刮卡配置创建失败"
            showResultWithAnimation()
            print("❌ 刮刮卡配置创建失败")
        }
    }
    
    /**
     * 设置测试成员并显示刮刮卡
     */
    private func setupTestMemberAndShowScratchCard() {
        guard let member = testMember else {
            selectedOption = "请先创建测试成员"
            showResultWithAnimation()
            return
        }
        
        // 检查是否有刮刮卡配置
        let config = dataManager.getScratchCardConfig(for: member)
        if config == nil {
            selectedOption = "请先配置刮刮卡"
            showResultWithAnimation()
            return
        }
        
        showScratchCard = true
    }
    
    /**
     * 显示结果动画
     */
    private func showResultWithAnimation() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            showResult = true
        }
        
        // 3秒后自动隐藏结果
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            withAnimation(.easeOut(duration: 0.5)) {
                showResult = false
            }
        }
    }
}

// MARK: - Preview

#Preview {
    ScratchCardTestView()
        .environmentObject(DataManager.shared)
}
